apply plugin: "com.android.application"
apply plugin: "org.jetbrains.kotlin.android"
apply plugin: "com.facebook.react"

apply plugin: 'com.google.gms.google-services'

apply from: file("../../node_modules/react-native-vector-icons/fonts.gradle")
/**
 * This is the configuration block to customize your React Native Android app.
 * By default you don't need to apply any configuration, just uncomment the lines you need.
 */
react {
    /* Autolinking */
    autolinkLibrariesWithApp()
}

def enableSeparateBuildPerCPUArchitecture = false

def enableProguardInReleaseBuilds = false

def jscFlavor = 'org.webkit:android-jsc:+'

android {
    ndkVersion rootProject.ext.ndkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdk rootProject.ext.compileSdkVersion
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    namespace "vn.tgcorp.pronexus"
    defaultConfig {
        applicationId "vn.tgcorp.pronexus"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 64
        versionName "1.0.64"
        multiDexEnabled true
        vectorDrawables.useSupportLibrary = true
        testBuildType System.getProperty('testBuildType', 'debug')
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
        missingDimensionStrategy 'react-native-camera', 'general'

    }

    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'pronexusdebug'
            keyPassword 'android'
        }
        // Cấu hình cho việc test local
        release {
            storeFile file(RELEASE_STORE_FILE)
            storePassword RELEASE_STORE_PASSWORD
            keyAlias RELEASE_KEY_ALIAS
            keyPassword RELEASE_KEY_PASSWORD
        }
        // Cấu hình cho việc upload lên Google Play
        upload {
            storeFile file(UPLOAD_STORE_FILE)
            storePassword UPLOAD_STORE_PASSWORD
            keyAlias UPLOAD_KEY_ALIAS
            keyPassword UPLOAD_KEY_PASSWORD
        }
    }
    buildTypes {
        debug {
            signingConfig signingConfigs.debug
        }
        // Bản release cho test local
        release {
            // Caution! In production, you need to generate your own keystore file.
            // see https://facebook.github.io/react-native/docs/signed-apk-android.
            signingConfig signingConfigs.release
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
            crunchPngs false  // Paste this line
            // Detox-specific additions to pro-guard
            proguardFile "${rootProject.projectDir}/../node_modules/detox/android/detox/proguard-rules-app.pro"
        }
        // Bản release để upload lên Google Play
        googlePlay {
            initWith release
            signingConfig signingConfigs.upload
            matchingFallbacks = ['release']
        }
    }
}

dependencies {
    // The version of react-native is set by the React Native Gradle Plugin
    implementation("com.facebook.react:react-android")
    implementation 'com.facebook.fresco:fresco:2.0.0'
    implementation 'com.facebook.fresco:animated-gif:2.0.0'
    implementation 'me.leolin:ShortcutBadger:1.1.22@aar' // <-- Add this line if you wish to use badge on Android

    implementation 'com.facebook.android:facebook-android-sdk:latest.release'

    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }
    implementation platform('com.google.firebase:firebase-bom:32.0.0')

    implementation 'com.google.firebase:firebase-messaging'

    implementation 'com.google.firebase:firebase-analytics'
    implementation('com.google.firebase:firebase-auth') {
        exclude module: "play-services-safetynet"
    }

    implementation platform('com.squareup.okhttp3:okhttp-bom:4.9.3')
    implementation 'com.squareup.okhttp3:okhttp'
    implementation 'com.squareup.okhttp3:logging-interceptor'


    androidTestImplementation('com.wix:detox:+') { transitive = true }
    androidTestImplementation 'junit:junit:4.12'
    implementation "androidx.annotation:annotation:1.1.0"

    implementation(platform("org.jetbrains.kotlin:kotlin-bom:1.8.0"))

    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }

}
