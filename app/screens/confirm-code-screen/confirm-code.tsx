import { SafeAreaView } from 'react-native-safe-area-context'
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { Text, View, TouchableOpacity, Alert, TextInput, ActivityIndicator } from 'react-native'
import React, { useContext, useEffect, useState, useRef } from 'react'
import { useRoute, useNavigation } from '@react-navigation/native'
import Icon from 'react-native-vector-icons/Ionicons'

import { observer } from 'mobx-react-lite'
import { useStores } from '../../models/root-store'
import { TButton, ButtonBack } from '@app/components'
import { SCREENS } from '@app/navigation'
import { useTranslation } from 'react-i18next'
import { ModalContext } from '@app/context'
import validate from 'validate.js'
import { useAuth } from '@app/use-hooks/use-auth'
import { color, typography } from '@app/theme'
import SimpleToast from 'react-native-simple-toast'
import styles from './styles'
import { GenerateOtpResponseData } from '@app/services/api/interface/otp.interface.ts';
import { PASSWORD_RESET, REGISTRATION, RESET_PASSCODE } from '@app/constants/confirm-code-type'

const TIME = 10
const CELL_COUNT = 6

interface VerificationCodeInputProps {
  value: string;
  onChangeText: (text: string) => void;
}

const VerificationCodeInput = ({ value, onChangeText }: VerificationCodeInputProps) => {
  const inputRefs = useRef<Array<TextInput | null>>([])
  const digits = value.split('')
  const emptyDigits = Array(CELL_COUNT - digits.length).fill('')
  const displayValue = digits.map(d => d ? '•' : '')

  const handleChangeText = (text: string, index: number) => {
    const newValue = value.split('')
    if (text === '') {
      newValue[index] = ''
      onChangeText(newValue.join(''))
      if (index > 0) {
        inputRefs.current[index - 1]?.focus()
      }
    } else {
      newValue[index] = text
      onChangeText(newValue.join(''))
      if (index < CELL_COUNT - 1) {
        inputRefs.current[index + 1]?.focus()
      }
    }
  }

  return (
    <View style={styles.passcodeContainer}>
      <View style={styles.digitsContainer}>
        {[...digits, ...emptyDigits].slice(0, CELL_COUNT).map((digit, index) => (
          <View key={index} style={styles.digitBox}>
            <TextInput
              ref={ref => inputRefs.current[index] = ref}
              value={displayValue[index]}
              onChangeText={(text) => handleChangeText(text, index)}
              maxLength={1}
              keyboardType="numeric"
              style={styles.digitInput}
              textAlign="center"
            />
          </View>
        ))}
      </View>
    </View>
  )
}
export const ConfirmCode = observer((props: any) => {
  const { t } : any = useTranslation()
  const { navigate, goBack } = useNavigation<any>()
  const route: any = useRoute()
  const { accountStore, profileStore } = useStores()
  const [value, setValue] = useState('')
  const debounceTimer = useRef<ReturnType<typeof setTimeout> | null>(null)

  // Auto-submit when 6 digits entered and user stops typing for 0.5s
  useEffect(() => {
    if (value.length === CELL_COUNT) {
      if (debounceTimer.current) clearTimeout(debounceTimer.current)
      debounceTimer.current = setTimeout(() => {
        confirmCode()
      }, 500)
    } else {
      if (debounceTimer.current) clearTimeout(debounceTimer.current)
    }
    // Cleanup on unmount
    return () => {
      if (debounceTimer.current) clearTimeout(debounceTimer.current)
    }
  }, [value])
  
  const phoneNumber = route.params?.phoneNumber
  const { showError, showSuccess } = useContext(ModalContext)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isResend, setIsResend] = useState(true)
  const [otpResult, setOtpResult] = useState<GenerateOtpResponseData | null>(null)
  const validateFields = () => validate.isEmpty(value)
  const { data,
    otpType } = props?.route?.params || {}

  // @ts-ignore
  const { signOut, userToken, signUp, signIn } = useAuth() // should be signUp

  const [otpExpirySeconds, setOtpExpirySeconds] = useState(0)
  const [resendTimerSeconds, setResendTimerSeconds] = useState(0)

  // Countdown timer for OTP expiration
  useEffect(() => {
    if (otpExpirySeconds <= 0) return

    const expiryTimerId = setInterval(() => {
      setOtpExpirySeconds(prev => {
        if (prev <= 1) {        
          clearInterval(expiryTimerId)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(expiryTimerId)
  }, [otpExpirySeconds])
  
  // Separate countdown timer for resend functionality
  useEffect(() => {
    if (resendTimerSeconds <= 0) return

    const resendTimerId = setInterval(() => {
      setResendTimerSeconds(prev => {
        if (prev <= 1) {
          clearInterval(resendTimerId)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(resendTimerId)
  }, [resendTimerSeconds])

  // Gọi API generate OTP khi màn hình mở
  const generateOtpCode = async () => {
    if (resendTimerSeconds > 0) {
      showError(t('FAIL'), t('Vui lòng đợi ' + resendTimerSeconds + ' giây để gửi lại'))
      return
    }
    try {
      setIsSubmitting(true)
      const result = await accountStore.generateOtp(
        phoneNumber,
        otpType,
        "ZNS",
        `Xác thực ${otpType}`
      )

      if (result.kind === "ok" && result.data?.success) {
        setOtpResult(result?.data)
        // Lưu thời gian hết hạn từ API response
        const remainingTime = result.data?.details?.remainingTimeInSeconds || 0
        setOtpExpirySeconds(remainingTime)
        // Reset resend timer to 120s regardless of OTP expiry time
        setResendTimerSeconds(120)
        setIsSubmitting(false)
        SimpleToast.show("Đã gửi mã xác nhận")
      } else {
        // Xử lý các trường hợp lỗi khác nhau
        if (result.kind === "bad-data") {
          showError(t("FAIL"), t("Có lỗi xảy ra khi gửi mã xác thực"))
        } else if (result.kind === "server" || result.kind === "unauthorized" || result.kind === "forbidden") {
          showError(t("FAIL"), result.data?.message || t("Lỗi máy chủ, vui lòng thử lại sau"))
        } else if (result.kind === "ok" && !result.data?.success) {
          // Trường hợp API trả về thành công nhưng không gửi được OTP
          showError(t("FAIL"), result?.data?.message || t("Không thể gửi mã xác thực"))
        } else {
          showError(t("FAIL"), result?.data?.message || t("Có lỗi xảy ra khi gửi mã xác thực"))
        }
      }
    } catch (error) {
      showError(t("FAIL"), t("Có lỗi xảy ra khi gửi mã xác thực"))
      __DEV__ && console.log("Error generating OTP:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  useEffect(() => {
    // Nếu không có số điện thoại và không phải là chế độ test, quay lại màn hình trước
    if ((!route.params?.phoneNumber || !phoneNumber) && !route.params?.isTestMode) {
      // comment for dev
      goBack()
      return;
    } else {
      generateOtpCode()
    }
  }, [])

  // function old
  const nextStepRegister = () => {
    if (otpType === REGISTRATION) {
      navigate(SCREENS.createPasswordNewAccount, { phoneNumber: accountStore?.phoneNumber, data: data })
      setValue('')
      setIsSubmitting(false)
    } else if (otpType === PASSWORD_RESET) {
      navigate(SCREENS.recoverPassword, { phoneNumber: accountStore?.phoneNumber, code: value })
      setValue('')
      setIsSubmitting(false)
    } else if (otpType === RESET_PASSCODE) {
      navigate(SCREENS.changePasscodeViaOtpScreen, { phoneNumber: phoneNumber, code: value, otpType: RESET_PASSCODE })
      setValue('')
      setIsSubmitting(false)
    } else {
      showError(t('FAIL'), t('Chưa cấu hình'))
      setIsSubmitting(false)
    }
  }


  const confirmCode = async () => {
    setIsSubmitting(true)
    if (!value) {
      showError(t('FAIL'), t('AUTHENTICATION_CODE_CANNOT_BE_EMPTY'))
      setIsSubmitting(false)
    } else {
      try {
        const result = await accountStore.verifyOtp(value, phoneNumber, otpType)
        __DEV__ && console.log('result verify', result)
        if (result.kind === 'ok' && result?.data?.success) {
          nextStepRegister()
        } else {
          setValue('')
          // Xử lý các trường hợp lỗi khác nhau
          if (result.kind === 'bad-data') {
            showError(t('FAIL'), t('Có lỗi xảy ra khi xác thực mã'))
          } else if (result.kind === 'server' || result.kind === 'unauthorized' || result.kind === 'forbidden') {
            showError(t('FAIL'), result?.data?.message || t('Lỗi máy chủ, vui lòng thử lại sau'))
          } else if (result.kind === 'ok' && !result?.data?.success) {
            // Trường hợp API trả về thành công nhưng xác thực thất bại
            const errorMessage = result?.data?.message || result?.data?.details?.message
            showError(t('FAIL'), errorMessage || t('Mã xác thực không hợp lệ'))

            // Hiển thị số lần thử còn lại nếu có
            const remainingAttempts = result?.data?.details?.remainingAttempts
            if (remainingAttempts !== null && remainingAttempts !== undefined) {
              SimpleToast.show(t('Còn lại {{count}} lần thử', { count: remainingAttempts }))
            }
          } else {
            showError(t('FAIL'), result?.data?.data?.message || t('Có lỗi xảy ra khi xác thực mã'))
          }
          setIsSubmitting(false)
        }
      } catch (error) {
        showError(t('FAIL'), t('Có lỗi xảy ra khi xác thực mã'))
        setIsSubmitting(false)
      }
    }
  }

  // Không cần renderCell nữa vì đã có component VerificationCodeInput
  return (
    <SafeAreaView style={styles.safeAreaView} >
      <ButtonBack onPress={goBack} style={styles.icArrowBack}/>
      <View style={styles.viewContainer}>
        <View>
          <Text style={styles.title}>{t('ENTER_AUTH_CODE')}</Text>
          <Text style={styles.subTitle}>
            {t('ENTER_THE_VERIFICATION_CODE')}
          </Text>
          <Text style={styles.phoneNumber}>
            {phoneNumber}
          </Text>
          { otpExpirySeconds > 0 && (
            <Text style={styles.subTitle}>
              {t('Vui lòng xác nhận trong thời gian')} {otpExpirySeconds} {t('giây')}
            </Text>
          )}
        </View>
        <VerificationCodeInput
          value={value}
          onChangeText={setValue}
        />
        <View style={{ flexDirection: 'column', marginTop: 0 }}>
          <Text style={styles.subTitle}>
            {t('YOU_DID_NOT_RECEIVE_VERIFICATION_CODE')}
          </Text>
          <TButton disabled={resendTimerSeconds > 0} loading={isSubmitting} title={resendTimerSeconds > 0 ? t('Gửi lại mã' + ' (' + resendTimerSeconds + ')') : t('Gửi lại mã')} onPress={generateOtpCode} testID="confirmCode.resendButton" />
        </View>

      </View>
    </SafeAreaView>
  )
})
