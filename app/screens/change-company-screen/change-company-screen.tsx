import { SafeAreaView } from 'react-native-safe-area-context'
import React, { useContext, useEffect, useRef, useState } from 'react'
import { observer } from 'mobx-react-lite'
import {
  Text,
  TouchableOpacity,
  View,
  Dimensions,
  Alert
} from 'react-native'
import styles from './style'
import { useTranslation } from 'react-i18next'
import Icon from 'react-native-vector-icons/Ionicons'
import { useNavigation, useRoute } from '@react-navigation/native'
import { useStores } from '@app/models'
import {
  ButtonBack,
  TTextInput,
  TButton,
  LazyImage,
  ConfirmDialog
} from '@app/components'
import { Header } from 'react-native-elements'
import common, { linearGradientProps } from '@app/theme/styles/common'
// import { PickerSelect } from '@app/components/picker-select/picker-select'
import { remove } from '@app/utils/storage'
import { Modalize } from 'react-native-modalize'
// import { DEFAULT_DISTANCE } from '@app/constants/configs'
import { responsiveHeight } from 'react-native-responsive-dimensions'
import LinearGradient from 'react-native-linear-gradient'
import { color } from '@app/theme'
import { BookingType } from '@app/constants/bookingType'
import { ModalContext } from '@app/components/modal-success'
import FastImage from 'react-native-fast-image'
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view'
import { DATA_BANK } from '../profile-user-screen/databank'
import { RenderListBank } from './render-list-bank'
import { SCREENS } from '@app/navigation'
import ModalSuccess from '@app/components/modal-success/modal-success'
import { VerifyPasscodeScreen } from '../passcode-screen/verify-passcode-screen'
// import Animated from 'react-native-reanimated'

const initialLayout = { width: Dimensions.get('window').width }

interface UpdateProfileData {
  partnerId: string;
  code: string;
  identifierNo: string;
  bankAccounts: Array<{
    bankType: string | null;
    bankName: string;
    bankCode: string;
    ownerName: string | null;
    bankAccountNumber: string;
  }>;
}

export const ChangeCompanyScreen = observer((props: any) => {
  const modalChooseBank = useRef<Modalize>(null)
  const passcodeModalRef = useRef<Modalize>(null)
  const { navigate, goBack } = useNavigation<any>()
  const route = useRoute()
  const params = route?.params as any || {}
  const { prevScreen, selectedItem } = params

  const { t }: any = useTranslation()
  const { searchStore, profileStore } = useStores()
  const [bankCode, setBankCode] = useState('null')
  const [bankName, setBankName] = useState('')
  const [maNhanVien, setMaNhanVien] = useState('')
  const [phoneNumber, setPhoneNumber] = useState('')
  const [cccd, setCccd] = useState('')
  const [stk, setStk] = useState('')
  const refNear = useRef<any>(null)
  const refRate = useRef<any>(null)
  const refFavorite = useRef(null)
  const { showError, showSuccess, showCustomError, showCustomSuccess } = useContext(ModalContext)

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isShowConfirm, setIsShowConfirm] = useState(false)
  const [updateData, setUpdateData] = useState<UpdateProfileData | null>(null)

  const onGoBack = () => {
    goBack()
    searchStore.setKeyword('')
  }

  useEffect(() => {
    loadData()
    return () => {
      passcodeModalRef.current?.close()
    }
  }, [])

  const handleUpdate = async () => {
    // Collect validation errors
    const errors: string[] = [];
    
    if (!maNhanVien || maNhanVien.trim() === '') {
      errors.push('Vui lòng nhập mã nhân viên');
    }
    
    if (!cccd || cccd.trim() === '') {
      errors.push('Vui lòng nhập số CMND/CCCD');
    }
    
    if (bankCode === 'null' || !bankName) {
      errors.push('Vui lòng chọn ngân hàng');
    }
    
    if (!stk || stk.trim() === '') {
      errors.push('Vui lòng nhập số tài khoản');
    }
    
    // If there are validation errors, show them all in one popup
    if (errors.length > 0) {
      return showError('Thông báo', errors.join('\n'));
    }
    
    setIsShowConfirm(true);
  }

  const handleConfirm = async () => {
    setIsShowConfirm(false)
    prepareUpdateData()
    passcodeModalRef.current?.open()
  }

  const handleCancel = () => {
    setIsShowConfirm(false)
  }

  const prepareUpdateData = () => {
    const body: UpdateProfileData = {
      partnerId: selectedItem?.id || '',
      code: maNhanVien,
      identifierNo: cccd,
      bankAccounts: [
        {
          bankType: profileStore?.dataUserBank[0]?.bankType,
          bankName: bankName,
          bankCode: bankCode,
          ownerName: profileStore.fullName,
          bankAccountNumber: stk
        }
      ]
    }
    setUpdateData(body)
  }

  const processProfileUpdate = async () => {
    try {
      if (!updateData) return
      
      setIsSubmitting(true)
      const rs = await profileStore.updateProfile(updateData)
      setIsSubmitting(false)
      passcodeModalRef.current?.close()
      
      if (rs && rs.kind === 'ok' && rs.data.data.status === 'SUCCESS') {
        setTimeout(() => {
          navigate(SCREENS.updateProfileSuccessScreen as never)
        }, 300)
      } else {
        showError('', rs.data?.data?.message)
      }
    } catch (error) {
      setIsSubmitting(false)
      passcodeModalRef.current?.close()
      showError('Thông báo', 'Hệ thống đang gián đoạn. Quý khách vui lòng thử lại sau vài phút. Xin cảm ơn')
    }
  }

  const loadData = async () => {
    await remove('districtSelected')
    await searchStore.getDistrict()
  }

  const onOpenModalBank = () => {
    modalChooseBank.current?.open()
  }
  const onCloseModalBank = () => {
    modalChooseBank.current?.close()
  }


  const renderHeaderModalBank: any = () => (
    <View style={styles.modal__header}>
      <ButtonBack onPress={onCloseModalBank} style={styles.viewTouchButtonTop} />
      <Text style={styles.textTitleHeader}>{t('Ngân hàng')}</Text>
      <TouchableOpacity
        onPress={() => {
          setBankCode('null')
        }}
      >
        <Text style={styles.textClose}>{t('Tất cả')}</Text>
      </TouchableOpacity>
      {/* <View style={{ width: 20 }}></View> */}
    </View>
  )

  const renderInputLabel = (title) => {
    return (
      <Text style={styles.inputLabel}>{title}</Text>
    )
  }

  const iconLeft = <Icon name={'chevron-down-outline'} size={20} color={color.primary} style={styles.iconLeft} />
  return (
    <View style={styles.safeAreaView} >

      <Header
        leftComponent={<ButtonBack style={common.buttonBack} onPress={onGoBack} />}
        centerComponent={{ text: t(t('Cập nhật thông tin')), style: common.headerCenterTitle }}
        containerStyle={[common.headerContainer]}
        statusBarProps={{ barStyle: 'dark-content' }}
      //ViewComponent={LinearGradient}
      //linearGradientProps={linearGradientProps}
      />

      <KeyboardAwareScrollView>
        <View style={styles.contentView}>
          <View style={styles.viewDanhMuc}>
            <LazyImage mode={'download'} resizeMode={FastImage.resizeMode.contain} style={styles.imageCat as any} source={{uri: selectedItem?.logoId}} />
            <View style={{ marginLeft: 20, flex: 1 }}>
              <Text style={styles.itemName}>{selectedItem?.name}</Text>
              <Text style={styles.itemAddress}>{selectedItem?.add}</Text>
            </View>
          </View>
          {renderInputLabel('Mã nhân viên')}
          <TTextInput
            typeInput={'code'}
            typeRadius={'rounded'}
            // keyboardType="phone-pad"
            maxLength={255}
            autoCapitalize={'sentences'}
            value={maNhanVien}
            placeholder={t('Nhập mã số nhân viên')}
            onChangeText={e => setMaNhanVien(e)}
          />
          {renderInputLabel('Số điện thoại')}
          <TTextInput
            typeInput={'code'}
            typeRadius={'rounded'}
            keyboardType="phone-pad"
            maxLength={10}
            autoCapitalize={'sentences'}
            defaultValue={''}
            value={phoneNumber}
            validatePhone={true}
            placeholder={t('Nhập số điện thoại')}
            onChangeText={e => setPhoneNumber(e)}
          />
          {renderInputLabel('CMND/CCCD')}
          <TTextInput
            typeInput={'code'}
            typeRadius={'rounded'}
            maxLength={12}
            autoCapitalize={'sentences'}
            defaultValue={''}
            keyboardType="numeric"
            value={cccd}
            placeholder={t('Nhập số CMND/CCCD')}
            onChangeText={e => setCccd(e)}
          />
          <Text style={{ color: '#526B7A', marginTop: 24 }}>
            Nếu doanh nghiệp của bạn không có mã nhân viên thì nhập số điện thoại di động thay thế.
          </Text>
        </View>
        <View style={[styles.contentView, { marginBottom: 20 }]}>
          {renderInputLabel('Ngân hàng')}
          <TouchableOpacity style={styles.viewProvince} onPress={() => onOpenModalBank()}>
            <Text style={{ ...styles.textProvince, color: bankName ? '#333' : '#A0A0A0' }}
            >{bankName || 'Chọn ngân hàng'}</Text>
            {iconLeft}
          </TouchableOpacity>
          {renderInputLabel('Số tài khoản')}
          <TTextInput
            typeInput={'code'}
            typeRadius={'rounded'}
            maxLength={255}
            autoCapitalize={'sentences'}
            defaultValue={''}
            value={stk}
            placeholder={t('Nhập số tài khoản')}
            onChangeText={e => setStk(e)}
          />

        </View>
      </KeyboardAwareScrollView>
      <View style={styles.footerView}>
        <TButton loading={isSubmitting} typeRadius={'rounded'} title="Cập nhật"
          onPress={() => handleUpdate()}
        />
      </View>

      <Modalize
        HeaderComponent={renderHeaderModalBank}
        ref={modalChooseBank}
        modalTopOffset={40}
        // adjustToContentHeight
        // snapPoint={405}
        modalHeight={responsiveHeight(70)}
        // onClosed={() => { props.onClose(true) }}
        keyboardAvoidingBehavior={'padding'}
      >
        <RenderListBank data={DATA_BANK.Data.BankInfor} bankCode={bankCode} onSelect={(e) => {
          setBankCode(e.label)
          setBankName(e.Description)
          onCloseModalBank()
        }} />
      </Modalize>

      <Modalize
        ref={passcodeModalRef}
        adjustToContentHeight
        modalStyle={{ backgroundColor: '#fff', borderTopLeftRadius: 12, borderTopRightRadius: 12 }}
      >
        <VerifyPasscodeScreen 
          onSuccess={processProfileUpdate}
        />
      </Modalize>

      <ConfirmDialog
        confirmText={t('CONFIRM')}
        cancelText={t('CANCEL')}
        onClosed={handleCancel}
        isVisible={isShowConfirm}
        message={t('Bạn có chắc chắn muốn cập nhật thông tin công ty?')}
        title={t('NOTIFICATION')}
        onConfirm={handleConfirm}
      />
    </View>
  )
})
