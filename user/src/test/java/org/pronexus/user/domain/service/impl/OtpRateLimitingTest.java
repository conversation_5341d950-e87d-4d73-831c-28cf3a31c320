package org.pronexus.user.domain.service.impl;

import com.salaryadvance.commonlibrary.rest.CommandResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.pronexus.user.app.dtos.otp.GenerateOtpRequestDto;
import org.pronexus.user.app.dtos.otp.GenerateOtpResponseDto;
import org.pronexus.user.domain.entities.OtpAttempt;
import org.pronexus.user.domain.entity.OtpDeliveryMethod;
import org.pronexus.user.domain.entity.OtpType;
import org.pronexus.user.domain.repositories.OtpAttemptRepository;
import org.pronexus.user.domain.repositories.OtpRepository;
import org.pronexus.user.domain.mapper.OtpMapper;
import org.pronexus.user.domain.service.core.ZnsService;
import org.pronexus.user.domain.feign.adapter.KeycloakClient;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Test class for OTP rate limiting functionality
 */
@ExtendWith(MockitoExtension.class)
class OtpRateLimitingTest {

    @Mock
    private OtpRepository otpRepository;

    @Mock
    private OtpAttemptRepository otpAttemptRepository;

    @Mock
    private OtpMapper otpMapper;

    @Mock
    private ZnsService znsService;

    @Mock
    private KeycloakClient keycloakClient;

    @InjectMocks
    private OtpServiceImpl otpService;

    private static final String TEST_PHONE = "0123456789";
    private GenerateOtpRequestDto requestDto;

    @BeforeEach
    void setUp() {
        requestDto = GenerateOtpRequestDto.builder()
                .type(OtpType.PASSWORD_RESET)
                .contactInfo(TEST_PHONE)
                .deliveryMethod(OtpDeliveryMethod.ZNS)
                .build();
    }

    @Test
    void testRateLimiting_FirstAttempt_ShouldSucceed() {
        // Given
        when(otpAttemptRepository.findByContactInfo(TEST_PHONE)).thenReturn(Optional.empty());
        
        // When
        CommandResponse<GenerateOtpResponseDto> response = otpService.generateOtp(requestDto);
        
        // Then
        assertTrue(response.isSuccess());
        verify(otpAttemptRepository).save(any(OtpAttempt.class));
    }

    @Test
    void testRateLimiting_ThirdAttempt_ShouldSucceed() {
        // Given
        OtpAttempt attempt = OtpAttempt.builder()
                .contactInfo(TEST_PHONE)
                .attemptCount(2)
                .firstAttemptTime(LocalDateTime.now().minusMinutes(10))
                .lastAttemptTime(LocalDateTime.now().minusMinutes(5))
                .build();
        
        when(otpAttemptRepository.findByContactInfo(TEST_PHONE)).thenReturn(Optional.of(attempt));
        
        // When
        CommandResponse<GenerateOtpResponseDto> response = otpService.generateOtp(requestDto);
        
        // Then
        assertTrue(response.isSuccess());
    }

    @Test
    void testRateLimiting_FourthAttempt_ShouldBeBlocked() {
        // Given
        OtpAttempt attempt = OtpAttempt.builder()
                .contactInfo(TEST_PHONE)
                .attemptCount(3)
                .firstAttemptTime(LocalDateTime.now().minusMinutes(10))
                .lastAttemptTime(LocalDateTime.now().minusMinutes(5))
                .build();
        
        when(otpAttemptRepository.findByContactInfo(TEST_PHONE)).thenReturn(Optional.of(attempt));
        
        // When
        CommandResponse<GenerateOtpResponseDto> response = otpService.generateOtp(requestDto);
        
        // Then
        assertFalse(response.isSuccess());
        assertEquals("Quý khách yêu cầu mã OTP quá 3 lần, vui lòng đăng ký lại sau 30 phút.", response.getMessage());
    }

    @Test
    void testRateLimiting_AfterCooldown_ShouldReset() {
        // Given
        OtpAttempt attempt = OtpAttempt.builder()
                .contactInfo(TEST_PHONE)
                .attemptCount(3)
                .firstAttemptTime(LocalDateTime.now().minusMinutes(35)) // More than 30 minutes ago
                .lastAttemptTime(LocalDateTime.now().minusMinutes(32))
                .build();
        
        when(otpAttemptRepository.findByContactInfo(TEST_PHONE)).thenReturn(Optional.of(attempt));
        
        // When
        CommandResponse<GenerateOtpResponseDto> response = otpService.generateOtp(requestDto);
        
        // Then
        assertTrue(response.isSuccess());
        verify(otpAttemptRepository).save(any(OtpAttempt.class));
    }
}
