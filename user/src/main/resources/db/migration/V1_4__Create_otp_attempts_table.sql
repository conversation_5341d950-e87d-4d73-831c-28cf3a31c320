-- Migration script to create otp_attempts table for rate limiting
-- Author: System
-- Date: 2024-12-19
-- Description: Create table to track OTP generation attempts for rate limiting (max 3 attempts per 30 minutes)

CREATE TABLE IF NOT EXISTS user_service.otp_attempts (
    id BIGSERIAL PRIMARY KEY,
    contact_info VARCHAR(255) NOT NULL UNIQUE,
    attempt_count INTEGER NOT NULL DEFAULT 0,
    first_attempt_time TIMESTAMP NOT NULL,
    last_attempt_time TIMESTAMP NOT NULL,
    created_at BIGINT NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW()) * 1000,
    updated_at BIGINT NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW()) * 1000,
    created_by VA<PERSON>HA<PERSON>(255) DEFAULT 'system',
    updated_by VARCHAR(255) DEFAULT 'system'
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_otp_attempts_contact_info ON user_service.otp_attempts(contact_info);
CREATE INDEX IF NOT EXISTS idx_otp_attempts_first_attempt_time ON user_service.otp_attempts(first_attempt_time);

-- Add comments
COMMENT ON TABLE user_service.otp_attempts IS 'Table to track OTP generation attempts for rate limiting';
COMMENT ON COLUMN user_service.otp_attempts.contact_info IS 'Contact info (phone number/email) used as key for tracking';
COMMENT ON COLUMN user_service.otp_attempts.attempt_count IS 'Number of OTP generation attempts in current window';
COMMENT ON COLUMN user_service.otp_attempts.first_attempt_time IS 'Timestamp of first attempt in current 30-minute window';
COMMENT ON COLUMN user_service.otp_attempts.last_attempt_time IS 'Timestamp of most recent attempt';
