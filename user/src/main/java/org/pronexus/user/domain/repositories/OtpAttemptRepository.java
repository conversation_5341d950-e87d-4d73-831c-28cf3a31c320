package org.pronexus.user.domain.repositories;

import org.pronexus.user.domain.entities.OtpAttempt;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * Repository cho OtpAttempt entity
 * Quản lý rate limiting cho OTP generation
 */
@Repository
public interface OtpAttemptRepository extends JpaRepository<OtpAttempt, Long> {

    /**
     * Tìm OTP attempt record theo contact info
     *
     * @param contactInfo Contact info (phone number/email)
     * @return OtpAttempt record nếu có
     */
    Optional<OtpAttempt> findByContactInfo(String contactInfo);

    /**
     * Xóa các record cũ đã hết hạn (quá 30 phút)
     * Cleanup job để giữ database sạch
     *
     * @param cutoffTime Thời gian cutoff (hiện tại - 30 phút)
     * @return Số record đã xóa
     */
    @Modifying
    @Query("DELETE FROM OtpAttempt o WHERE o.firstAttemptTime < :cutoffTime")
    int deleteExpiredAttempts(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * Đếm số user đang bị block
     *
     * @param cutoffTime Thời gian cutoff để xác định block
     * @return Số user bị block
     */
    @Query("SELECT COUNT(o) FROM OtpAttempt o WHERE o.attemptCount >= 3 AND o.firstAttemptTime > :cutoffTime")
    long countBlockedUsers(@Param("cutoffTime") LocalDateTime cutoffTime);
}
