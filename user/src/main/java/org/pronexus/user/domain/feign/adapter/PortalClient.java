package org.pronexus.user.domain.feign.adapter;

import com.salaryadvance.commonlibrary.exception.base.ExternalServiceException;
import com.salaryadvance.commonlibrary.rest.CommandResponse;
import com.salaryadvance.commonlibrary.rest.Response;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.pronexus.user.app.dtos.UserPasscodeInfoDto;
import org.pronexus.user.domain.feign.PortalFeignClient;
import org.pronexus.user.domain.model.external.EmployeeModel;
import org.pronexus.user.domain.model.external.PartnerModel;
import org.pronexus.user.domain.model.external.UserDeviceRes;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

@Slf4j
@Component
@RequiredArgsConstructor
public class PortalClient {

    private final KeycloakClient keycloakClient;
    private final PortalFeignClient portalFeignClient;

    public EmployeeModel queryEmployee(String query) {
        String token = "Bearer " + keycloakClient.getClientToken();
        try {
            Response<EmployeeModel> response = portalFeignClient.queryEmployee(token, query);
            return response.getData();
        } catch (FeignException e) {
            log.error("[QUERY EMPLOYEE] Employee not found with query {}", query, e);
            return null;
        }
    }

    public EmployeeModel getEmployeeByPhoneNumber(String phoneNumber) {
        String token = "Bearer " + keycloakClient.getClientToken();
        try {
            String query = "phoneNumber:eq:" + phoneNumber;
            Response<EmployeeModel> response = portalFeignClient.queryEmployee(token, query);
            return response.getData();
        } catch (FeignException e) {
            log.error("[SEARCH EMPLOYEE] Employee not found with phone number {}", phoneNumber, e);
            return null;
        }
    }

    public EmployeeModel getEmployeeByUserId(String userId) {
        String token = "Bearer " + keycloakClient.getClientToken();
        try {
            String query = "userId:eq:" + userId;
            Response<EmployeeModel> response = portalFeignClient.queryEmployee(token, query);
            return response.getData();
        } catch (FeignException e) {
            log.error("[SEARCH EMPLOYEE] Cannot get employee of id {}: {}", userId, e.getMessage(), e);
            return null;
        }
    }

    public PartnerModel getPartnerById(Long id) {
        String token = "Bearer " + keycloakClient.getClientToken();
        try {
            Response<PartnerModel> response = portalFeignClient.getPartner(token, id);
            return response.getData();
        } catch (FeignException e) {
            log.error("[SEARCH PARTNER] Cannot get partner of id {}: {}", id, e.getMessage(), e);
            return null;
        }
    }

    /**
     * get partner by user id of keycloak
     * 
     * @param id
     * @return
     */
    public PartnerModel getPartnerByKeyCloakId(String id) {
        String token = "Bearer " + keycloakClient.getClientToken();
        try {
            Response<PartnerModel> response = portalFeignClient.getPartnerByKeycloakId(token, id);
            return response.getData();
        } catch (FeignException e) {
            log.error("[SEARCH PARTNER] Cannot get partner of keycloak user id {}: {}", id, e.getMessage(), e);
            return null;
        }
    }

    /**
     * Xóa liên kết user-partner
     * 
     * @param userId ID của user
     * @return true nếu thành công
     */
    public boolean deleteUserPartner(String userId) {
        String token = "Bearer " + keycloakClient.getClientToken();
        try {
            Response<Boolean> response = portalFeignClient.deleteUserPartner(token, userId);
            return response.getData() != null && response.getData();
        } catch (FeignException e) {
            log.error("[DELETE USER_PARTNER] Cannot delete user_partner for user {}: {}", userId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Xóa tất cả thiết bị của user
     * 
     * @param userId ID của user
     * @return true nếu thành công
     */
    public boolean deleteUserDevices(String userId) {
        String token = "Bearer " + keycloakClient.getClientToken();
        try {
            Response<Boolean> response = portalFeignClient.deleteUserDevices(token, userId);
            return response.getData() != null && response.getData();
        } catch (FeignException e) {
            log.error("[DELETE USER_DEVICES] Cannot delete user devices for user {}: {}", userId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Lấy danh sách thiết bị của user
     * 
     * @param userId ID của user
     * @return danh sách thiết bị
     */
    public List<UserDeviceRes> getUserDevices(String userId) {
        String token = "Bearer " + keycloakClient.getClientToken();
        try {
            Response<List<UserDeviceRes>> response = portalFeignClient.getUserDevices(token, userId);
            return response.getData() != null ? response.getData() : Collections.emptyList();
        } catch (FeignException e) {
            log.error("[GET USER_DEVICES] Cannot get user devices for user {}: {}", userId, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    public void updateEmployee(Long employeeId, EmployeeModel employeeModel) {
        String token = "Bearer " + keycloakClient.getClientToken();
        try {
            // Debug log before modification
            try {
                ObjectMapper mapper = new ObjectMapper();
                mapper.registerModule(new JavaTimeModule());
                mapper.setSerializationInclusion(com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL);
                log.info("[UPDATE EMPLOYEE] Before modification - Employee model: {}", 
                        mapper.writerWithDefaultPrettyPrinter().writeValueAsString(employeeModel));
            } catch (Exception ex) {
                log.warn("[UPDATE EMPLOYEE] Could not log employee model before modification: {}", ex.getMessage());
            }

            // Avoid null bankAccounts to prevent serialization issues
            if (employeeModel.getBankAccounts() != null && employeeModel.getBankAccounts().isEmpty()) {
                employeeModel.setBankAccounts(null);
                log.info("[UPDATE EMPLOYEE] Empty bankAccounts set to null");
            }
            
            // Instead of just cloning, create a special version for the API call that fixes serialization issues
            EmployeeModel employeeModelToSend = null;
            
            try {
                // First convert to JSON
                ObjectMapper mapper = new ObjectMapper();
                mapper.registerModule(new JavaTimeModule());
                mapper.setSerializationInclusion(com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL);
                
                // Convert to JsonNode tree first
                JsonNode rootNode = mapper.valueToTree(employeeModel);
                
                // Fix dateOfBirth field - convert from array format to string format
                if (employeeModel.getDateOfBirth() != null) {
                    // The Portal service expects dateOfBirth as a string in ISO format
                    String dateOfBirthStr = employeeModel.getDateOfBirth().format(DateTimeFormatter.ISO_DATE);
                    ((ObjectNode)rootNode).put("dateOfBirth", dateOfBirthStr);
                    log.info("[UPDATE EMPLOYEE] Converted dateOfBirth from LocalDate to String: {}", dateOfBirthStr);
                }
                
                // Convert back to EmployeeModel
                employeeModelToSend = mapper.treeToValue(rootNode, EmployeeModel.class);
                
                // Debug log
                log.info("[UPDATE EMPLOYEE] After serialization fix - Employee model JSON: {}", 
                        mapper.writerWithDefaultPrettyPrinter().writeValueAsString(employeeModelToSend));
            } catch (Exception ex) {
                log.warn("[UPDATE EMPLOYEE] Could not fix dateOfBirth serialization: {}", ex.getMessage());
                // Fallback to the regular cloned model
                employeeModelToSend = cloneEmployeeModel(employeeModel);
            }

            CommandResponse<EmployeeModel> response = portalFeignClient.updateEmployee(token, employeeId, employeeModelToSend)
                    .getData();
            if (!response.isSuccess()) {
                log.error("[UPDATE EMPLOYEE] Failed to sync employee with user {}, message: {}", employeeId,
                        response.getMessage());
            }
        } catch (FeignException e) {
            // Log the full response body for debugging
            log.error("[UPDATE EMPLOYEE] Failed to sync employee with user, status: {}, body: {}", 
                    e.status(), e.contentUTF8());
            log.error("[UPDATE EMPLOYEE] Exception details: {}", e.getMessage(), e);
            throw new ExternalServiceException("Lỗi khi cập nhật thông tin tài khoản nhân viên",
                    Map.of("details", e.getMessage()));
        }
    }

    /**
     * Xóa salary advance limit của employee
     *
     * @param employeeId ID của employee
     * @return true nếu thành công
     */
    public boolean deleteSalaryAdvanceLimit(String employeeId) {
        String token = "Bearer " + keycloakClient.getClientToken();
        try {
            Response<Boolean> response = portalFeignClient.deleteSalaryAdvanceLimit(token, employeeId);
            return response.getData() != null && response.getData();
        } catch (FeignException e) {
            log.error("[DELETE SALARY_ADVANCE_LIMIT] Cannot delete salary advance limit for employee {}: {}",
                    employeeId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Force delete employee - xóa vĩnh viễn employee khỏi database
     * CHỈ dành cho super admin khi force delete user
     *
     * @param employeeId ID của employee
     * @return true nếu thành công
     */
    public boolean deleteEmployee(Long employeeId) {
        String token = "Bearer " + keycloakClient.getClientToken();
        try {
            Response<CommandResponse<Void>> response = portalFeignClient.forceDeleteEmployee(token, employeeId);
            return response.getData() != null && response.getData().isSuccess();
        } catch (FeignException e) {
            log.error("🔥 [FORCE DELETE EMPLOYEE] Cannot force delete employee {}: {}", employeeId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Lấy thông tin passcode của user
     *
     * @param userId ID của user trong Keycloak
     * @return thông tin passcode của user, null nếu không tìm thấy
     */
    /**
     * Clone an EmployeeModel and adjust fields for proper serialization
     * 
     * @param original The original EmployeeModel
     * @return A clone with properly formatted fields for serialization
     */
    private EmployeeModel cloneEmployeeModel(EmployeeModel original) {
        if (original == null) {
            return null;
        }
        
        EmployeeModel clone = new EmployeeModel();
        clone.setId(original.getId());
        clone.setUserId(original.getUserId());
        clone.setAvatar(original.getAvatar());
        clone.setCode(original.getCode());
        clone.setName(original.getName());
        clone.setFirstName(original.getFirstName());
        clone.setLastName(original.getLastName());
        clone.setGender(original.getGender());
        
        // Handle dateOfBirth to prevent array deserialization error
        // The error suggests Portal service expects a String, not a LocalDate array
        // Error: Cannot deserialize value of type `java.lang.String` from Array value (token `JsonToken.START_ARRAY`)
        if (original.getDateOfBirth() != null) {
            // Convert to same LocalDate object - the custom serialization with Jackson and JavaTimeModule 
            // will handle the conversion to ISO format string automatically
            clone.setDateOfBirth(original.getDateOfBirth());
        }
        
        clone.setContractType(original.getContractType());
        clone.setAddress(original.getAddress());
        clone.setSalary(original.getSalary());
        clone.setPartnerId(original.getPartnerId());
        clone.setDepartmentId(original.getDepartmentId());
        clone.setTeamId(original.getTeamId());
        clone.setStatus(original.getStatus());
        clone.setIdentifierNo(original.getIdentifierNo());
        clone.setIssueDate(original.getIssueDate());
        clone.setIssuePlace(original.getIssuePlace());
        clone.setPhoneNumber(original.getPhoneNumber());
        clone.setEmail(original.getEmail());
        clone.setCanEditAccount(original.isCanEditAccount());
        clone.setBankAccounts(original.getBankAccounts());
        clone.setCreditLimit(original.getCreditLimit());
        clone.setDepartment(original.getDepartment());
        clone.setPosition(original.getPosition());
        clone.setNote(original.getNote());
        
        return clone;
    }

    public UserPasscodeInfoDto getUserPasscodeInfo(String userId) {
        String token = "Bearer " + keycloakClient.getClientToken();
        try {
            Response<UserPasscodeInfoDto> response = portalFeignClient.getUserPasscodeInfo(token, userId);
            return response.getData();
        } catch (FeignException e) {
            log.error("[GET USER_PASSCODE_INFO] Cannot get passcode info for user {}: {}", userId, e.getMessage(), e);
            return null;
        }
    }
}
