package org.pronexus.user.domain.entities;

import com.salaryadvance.commonlibrary.persistence.AuditableEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Entity để track số lần generate OTP của user
 * Dùng cho rate limiting - tối đa 3 lần trong 30 phút
 */
@Entity
@Table(name = "otp_attempts", schema = "user_schema")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OtpAttempt extends AuditableEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * Contact info (phone number/email) của user
     * Dùng làm key để track attempts
     */
    @Column(nullable = false, unique = true)
    private String contactInfo;

    /**
     * Số lần đã generate OTP
     */
    @Column(nullable = false)
    private Integer attemptCount = 0;

    /**
     * Thời gian attempt đầu tiên trong window hiện tại
     */
    @Column(nullable = false)
    private LocalDateTime firstAttemptTime;

    /**
     * Thời gian attempt cuối cùng
     */
    @Column(nullable = false)
    private LocalDateTime lastAttemptTime;

    /**
     * Kiểm tra xem có đang trong thời gian block không
     * Block 30 phút sau khi đạt 3 attempts
     */
    public boolean isBlocked() {
        if (attemptCount < 3) {
            return false;
        }
        
        // Kiểm tra xem đã hết 30 phút chưa kể từ attempt đầu tiên
        LocalDateTime blockEndTime = firstAttemptTime.plusMinutes(30);
        return LocalDateTime.now().isBefore(blockEndTime);
    }

    /**
     * Kiểm tra xem có cần reset attempt counter không
     * Reset sau 30 phút kể từ attempt đầu tiên
     */
    public boolean shouldResetAttempts() {
        LocalDateTime resetTime = firstAttemptTime.plusMinutes(30);
        return LocalDateTime.now().isAfter(resetTime);
    }

    /**
     * Reset attempt counter cho window mới
     */
    public void resetAttempts() {
        this.attemptCount = 0;
        this.firstAttemptTime = LocalDateTime.now();
        this.lastAttemptTime = LocalDateTime.now();
    }

    /**
     * Tăng attempt counter
     */
    public void incrementAttempt() {
        if (this.attemptCount == 0) {
            this.firstAttemptTime = LocalDateTime.now();
        }
        this.attemptCount++;
        this.lastAttemptTime = LocalDateTime.now();
    }
}
