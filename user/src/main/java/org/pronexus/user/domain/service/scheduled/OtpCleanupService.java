package org.pronexus.user.domain.service.scheduled;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.pronexus.user.domain.repositories.OtpAttemptRepository;
import org.pronexus.user.domain.service.core.ConfigurationService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * Service để cleanup các OTP attempt records cũ
 * Chạy định kỳ để giữ database sạch
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OtpCleanupService {

    private final OtpAttemptRepository otpAttemptRepository;
    private final ConfigurationService configurationService;
    
    @Value("${app.scheduler.otp-cleanup.fixed-rate}")
    private long otpCleanupFixedRate;
    
    @Value("${app.scheduler.blocked-users-stats.fixed-rate}")
    private long blockedUsersStatsFixedRate;
    
    // Kho<PERSON> cấu hình cho thời gian xoá OTP
    private static final String CONFIG_KEY_OTP_CLEANUP_MINUTES = "CONFIG_KEY_OTP_CLEANUP_MINUTES";
    
    // Khoá cấu hình cho thời gian kiểm tra user bị khoá
    private static final String CONFIG_KEY_BLOCKED_USERS_MINUTES = "CONFIG_KEY_BLOCKED_USERS_MINUTES";
    
    // Giá trị mặc định 30 phút
    private static final int DEFAULT_MINUTES = 30;

    /**
     * Cleanup các OTP attempt records cũ hơn 30 phút
     * Chạy mỗi giờ
     */
    @Scheduled(fixedRateString = "${app.scheduler.otp-cleanup.fixed-rate}")
    @Transactional
    public void cleanupExpiredOtpAttempts() {
        try {
            // Lấy thời gian từ cấu hình, mặc định là 30 phút nếu không có
            int minutes = getConfigMinutes(CONFIG_KEY_OTP_CLEANUP_MINUTES);
            LocalDateTime cutoffTime = LocalDateTime.now().minusMinutes(minutes);
            int deletedCount = otpAttemptRepository.deleteExpiredAttempts(cutoffTime);
            
            if (deletedCount > 0) {
                log.info("Cleaned up {} expired OTP attempt records", deletedCount);
            }
        } catch (Exception e) {
            log.error("Error during OTP attempts cleanup: {}", e.getMessage(), e);
        }
    }

    /**
     * Log thống kê về số user đang bị block
     * Chạy mỗi 30 phút
     */
    @Scheduled(fixedRateString = "${app.scheduler.blocked-users-stats.fixed-rate}")
    public void logBlockedUsersStats() {
        try {
            // Lấy thời gian từ cấu hình, mặc định là 30 phút nếu không có
            int minutes = getConfigMinutes(CONFIG_KEY_BLOCKED_USERS_MINUTES);
            LocalDateTime cutoffTime = LocalDateTime.now().minusMinutes(minutes);
            long blockedCount = otpAttemptRepository.countBlockedUsers(cutoffTime);
            
            if (blockedCount > 0) {
                log.info("Currently {} users are blocked from OTP generation", blockedCount);
            }
        } catch (Exception e) {
            log.error("Error getting blocked users stats: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Lấy giá trị số phút từ bảng cấu hình
     * @param configKey Khóa cấu hình
     * @return Giá trị số phút (mặc định là 30 nếu không tìm thấy hoặc có lỗi)
     */
    private int getConfigMinutes(String configKey) {
        try {
            return configurationService.getConfigurationValue(configKey)
                .map(Integer::parseInt)
                .orElse(DEFAULT_MINUTES);
        } catch (Exception e) {
            log.warn("Error getting configuration for {}, using default value: {}", configKey, DEFAULT_MINUTES, e);
            return DEFAULT_MINUTES;
        }
    }
}
